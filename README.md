# TVP Operator UI Tests

This repository contains Cypress tests for the TVP Operator UI application.

## Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

## Installation

Clone the repository and install dependencies:

```bash
git clone <repository-url>
cd <repository-directory>
npm install
```

## Running Tests

### Run all tests in headless mode

```bash
npm testnpm test
```

### Run tests in Chrome

```bash
npm run test:chrome
```

### Run tests in Firefox

```bash
npm run test:firefox
```

### Run tests in headed mode (with browser UI)

```bash
npm run test:headed
```

### Open Cypress Test Runner

```bash
npm run open
```

## Test Structure

- `cypress/e2e/` - Contains test files
- `cypress/pageObjects/` - Contains Page Object Model files
- `cypress/support/` - Contains support files and custom commands

## CI/CD Integration

This project is configured to run tests in Bitbucket Pipelines. The pipeline configuration is defined in `bitbucket-pipelines.yml`.

The pipeline consists of two steps:
1. Install dependencies
2. Run Cypress tests in headless mode

## Test Reports

Test results, screenshots, and videos are stored in the following directories:
- `cypress/videos/` - Test execution videos
- `cypress/screenshots/` - Screenshots taken during test failures

## Troubleshooting

If you encounter issues with the tests, try the following:

1. Clear Cypress cache:
   ```bash
   npx cypress cache clear
   ```

2. Verify Cypress installation:
   ```bash
   npx cypress verify
   ```

3. Check for any errors in the console output

## Notes

- Tests are configured to maintain session state between test cases
- Default timeouts are set to handle slow network conditions
- Tests include screenshots and videos for debugging
