class RedeemTokenPage {
    navigateToRedeemToken() {
        // First, check if we're on about:blank and if so, navigate to the main page
        cy.url().then(url => {
            if (url === 'about:blank') {
                cy.log('Detected about:blank page, navigating to main application');
                cy.visit('https://tvp-operator-ui.k8.isw.la/operator/dashboard');
                cy.wait(10000); // Wait for page to load
            }
        });

        // Add a wait before trying to navigate
        cy.wait(5000);

        cy.log('Attempting to navigate to Redeem Token page');

        // Take a screenshot to debug what's visible on the page
        cy.screenshot('before-redeem-token-navigation');

        // Try multiple approaches to find and click the Redeem Token link
        cy.get('body').then($body => {
            // Check if the Redeem Token element exists with different selectors

            // 1. Try with exact text match
            if ($body.find("p:contains('Redeem Token')").length > 0) {
                cy.log('Found Redeem Token using contains, scrolling to it');

                // First, scroll to the element to make it visible
                cy.contains('p', 'Redeem Token')
                  .scrollIntoView({ duration: 2000 })
                  .wait(1000) // Wait after scrolling
                  .click({ force: true });

                cy.log('Clicked on Redeem Token using contains');
                return;
            }

            // 2. Try with class-based selector
            if ($body.find("p.side_sub_txt.token").length > 0) {
                cy.log('Found Redeem Token using class selector, scrolling to it');

                // First, scroll to the element to make it visible
                cy.get('p.side_sub_txt.token:contains("Redeem")')
                  .scrollIntoView({ duration: 2000 })
                  .wait(1000) // Wait after scrolling
                  .click({ force: true });

                cy.log('Clicked on Redeem Token using class selector');
                return;
            }

            // 3. Try with XPath as last resort
            cy.log('Trying with XPath selector');

            // First, scroll to the element to make it visible
            cy.xpath("//p[contains(@class, 'side_sub_txt') and contains(@class, 'token') and contains(text(), 'Redeem Token')]")
              .scrollIntoView({ duration: 2000 })
              .wait(1000) // Wait after scrolling
              .click({ force: true });

            cy.log('Clicked on Redeem Token using XPath');
        });

        // Add a wait after clicking to allow page transition
        cy.wait(5000);

        // Take another screenshot after navigation attempt
        cy.screenshot('after-redeem-token-navigation');

        // Verify we're on the Redeem Token page by URL
        cy.url().should('include', '/redeem-token', { timeout: 30000 });

        // Log page information for debugging
        cy.get('body').then($body => {
            // Look for various elements that might be on the Redeem Token page
            const hasTokenInput = $body.find("input[data-testid='tokenInput']").length > 0;
            const hasTokenTypeDropdown = $body.find("div:contains('Token Type')").length > 0;
            const hasRedeemButton = $body.find("button:contains('Redeem')").length > 0;

            cy.log(`Page has token input: ${hasTokenInput}`);
            cy.log(`Page has token type dropdown: ${hasTokenTypeDropdown}`);
            cy.log(`Page has redeem button: ${hasRedeemButton}`);
        });
    }

    enterTokenID() {
        // Wait for the input field to be visible
        cy.xpath("//input[@data-testid='tokenInput']")
          .should('be.visible', { timeout: 30000 });

        // Clear any existing value first
        cy.xpath("//input[@data-testid='tokenInput']")
          .clear()
          .type("BPPWIIY", { force: true });

        // Verify the input has the value
        cy.xpath("//input[@data-testid='tokenInput']")
          .should('have.value', "BPPWIIY");

        cy.log('Entered token ID: BPPWIIY');

        // Take a screenshot after entering token ID
        cy.screenshot('after-enter-token-id');
    }

    selectTokenType() {
        // Click on the token type dropdown with force: true to handle if it's covered
        cy.xpath("//div[contains(@class,'css-1wa3eu0-placeholder') and text()='Token Type']")
          .scrollIntoView()
          .click({ force: true });

        // Wait for the dropdown to open
        cy.wait(2000);

        // Take a screenshot of the open dropdown
        cy.screenshot('token-type-dropdown-open');

        // Try to find and type in the input field
        cy.get('body').then($body => {
            const inputField = $body.find("input[aria-autocomplete='list']");

            if (inputField.length > 0) {
                cy.wrap(inputField)
                  .first()
                  .clear()
                  .type("Operator{enter}", { force: true });

                cy.log('Typed "Operator" into token type input field');
            } else {
                cy.log('Input field not found, trying with XPath');

                cy.xpath("//input[@aria-autocomplete='list']")
                  .clear()
                  .type("Operator{enter}", { force: true });
            }
        });

        // Wait for the selection to be processed
        cy.wait(2000);

        // Verify that Operator is selected
        cy.get('body').then($body => {
            const selectedType = $body.find(".css-1uccc91-singleValue, .css-selected-value").text();
            cy.log(`Selected token type: ${selectedType}`);

            // Take a screenshot after token type selection
            cy.screenshot('after-token-type-selection');
        });
    }

    clickRedeemButton() {
        // Click the Redeem button with force: true to handle if it's covered
        cy.xpath("//button[contains(@class, 'primary_btn') and text()='Redeem']")
          .should('be.visible', { timeout: 30000 })
          .click({ force: true });

        // Add a wait to ensure the redemption is processed
        cy.wait(5000);

        // Log for debugging
        cy.log('Clicked Redeem button');

        // Take a screenshot after clicking
        cy.screenshot('after-click-redeem');
    }

    checkRedemptionResult() {
        // Check for success or error messages
        cy.get('body').then($body => {
            const hasSuccess = $body.find(":contains('Token redeemed successfully'), :contains('Successful')").length > 0;
            const hasError = $body.find(":contains('Invalid token'), :contains('Error'), .error, .alert-error").length > 0;

            if (hasSuccess) {
                cy.log('Token redemption successful');
                cy.screenshot('token-redemption-success');
            } else if (hasError) {
                cy.log('Token BPPWIIY has been used');
                cy.screenshot('token-redemption-error');
            } else {
                cy.log('No redemption result found');
                cy.screenshot('no-redemption-result');
            }
        });
    }

    redeemToken() {
        // Enter the token ID
        this.enterTokenID();

        // Select the token type
        this.selectTokenType();

        // Click the redeem button
        this.clickRedeemButton();

        // Check the redemption result
        this.checkRedemptionResult();

        // Wait for any operations to complete
        cy.wait(3000);

        // Take a screenshot after token redemption
        cy.screenshot('after-token-redemption');
    }
}

export default RedeemTokenPage;