class LoginPage {
    visit() {
        cy.visit('https://tvp-operator-ui.k8.isw.la/operator/auth');
        cy.url().should('include', '/auth');
        cy.log('Visited login page');
    }

    enterEmail(email) {
        cy.get('#username').should('be.visible').type(email);
        cy.log(`Entered email: ${email}`);
    }

    enterPassword(password) {
        cy.get('input[name="password"]').should('be.visible').type(password);
        cy.log('Entered password');
    }

    clickLogin() {
        cy.get('.primary_btn').should('be.visible').click();
        cy.log('Clicked login button');

        // Wait for login to complete and verify we're redirected to dashboard
        cy.url().should('not.include', '/auth', { timeout: 30000 });
        cy.url().should('include', '/dashboard', { timeout: 30000 });
        cy.log('Login successful, redirected to dashboard');

        // Take a screenshot of the dashboard
        cy.screenshot('dashboard-after-login');

        // Ensure we're not on about:blank
        cy.url().should('not.eq', 'about:blank');
    }
}

export default LoginPage;