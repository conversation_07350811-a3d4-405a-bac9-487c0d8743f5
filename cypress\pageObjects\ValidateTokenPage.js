class ValidateTokenPage {
    navigateToValidateToken() {
        // First, check if we're on about:blank and if so, navigate to the main page
        cy.url().then(url => {
            if (url === 'about:blank') {
                cy.log('Detected about:blank page, navigating to main application');
                cy.visit('https://tvp-operator-ui.k8.isw.la/operator/dashboard');
                cy.wait(10000); // Wait for page to load
            }
        });

        // Add a wait before trying to navigate
        cy.wait(5000);

        cy.log('Attempting to navigate to Validate Token page');

        // Take a screenshot to debug what's visible on the page
        cy.screenshot('before-validate-token-navigation');

        // Try multiple approaches to find and click the Validate Token link
        cy.get('body').then($body => {
            // Check if the Validate Token element exists with different selectors

            // 1. Try with exact text match
            if ($body.find("p:contains('Validate Token')").length > 0) {
                cy.log('Found Validate Token using contains, scrolling to it');

                // First, scroll to the element to make it visible
                cy.contains('p', 'Validate Token')
                  .scrollIntoView({ duration: 2000 })
                  .wait(1000) // Wait after scrolling
                  .click({ force: true });

                cy.log('Clicked on Validate Token using contains');
                return;
            }

            // 2. Try with class-based selector
            if ($body.find("p.side_sub_txt.token").length > 0) {
                cy.log('Found Validate Token using class selector, scrolling to it');

                // First, scroll to the element to make it visible
                cy.get('p.side_sub_txt.token')
                  .scrollIntoView({ duration: 2000 })
                  .wait(1000) // Wait after scrolling
                  .click({ force: true });

                cy.log('Clicked on Validate Token using class selector');
                return;
            }

            // 3. Try with XPath as last resort
            cy.log('Trying with XPath selector');

            // First, scroll to the element to make it visible
            cy.xpath("//p[contains(@class, 'side_sub_txt') and contains(@class, 'token') and contains(text(), 'Validate Token')]")
              .scrollIntoView({ duration: 2000 })
              .wait(1000) // Wait after scrolling
              .click({ force: true });

            cy.log('Clicked on Validate Token using XPath');
        });

        // Add a wait after clicking to allow page transition
        cy.wait(5000);

        // Take another screenshot after navigation attempt
        cy.screenshot('after-validate-token-navigation');

        // Verify we're on the Validate Token page by URL
        cy.url().should('include', '/validate-token', { timeout: 30000 });

        // Log page information for debugging
        cy.get('body').then($body => {
            // Look for various elements that might be on the Validate Token page
            const hasTokenInput = $body.find("input[data-testid='tokenInput']").length > 0;
            const hasValidateButton = $body.find("button:contains('Validate')").length > 0;

            cy.log(`Page has token input: ${hasTokenInput}`);
            cy.log(`Page has validate button: ${hasValidateButton}`);
        });
    }

    enterTokenDetails() {
        // Wait for the input field to be visible
        cy.xpath("//input[@data-testid='tokenInput']")
          .should('be.visible', { timeout: 30000 });

        // Clear any existing value first
        cy.xpath("//input[@data-testid='tokenInput']")
          .clear()
          .type("K8P1LN3IDRD1", { force: true });

        // Verify the input has the value
        cy.xpath("//input[@data-testid='tokenInput']")
          .should('have.value', "K8P1LN3IDRD1");

        cy.log('Entered token: K8P1LN3IDRD1');

        // Take a screenshot after entering token
        cy.screenshot('after-enter-token');
    }

    clickValidateButton() {
        // Click the Validate button with force: true to handle if it's covered
        cy.xpath("//button[contains(@class, 'primary_btn') and text()='Validate']")
          .should('be.visible', { timeout: 30000 })
          .click({ force: true });

        // Add a wait to ensure the validation is processed
        cy.wait(5000);

        // Log for debugging
        cy.log('Clicked Validate button');

        // Take a screenshot after clicking
        cy.screenshot('after-click-validate');
    }

    checkValidationResult() {
        // Check for success or error messages
        cy.get('body').then($body => {
            const hasSuccess = $body.find(":contains('Token is valid'), :contains('Valid token')").length > 0;
            const hasError = $body.find(":contains('Invalid token'), :contains('Error'), .error, .alert-error").length > 0;

            if (hasSuccess) {
                cy.log('Token validation successful');
                cy.screenshot('token-validation-success');
            } else if (hasError) {
                cy.log('Token validation failed');
                cy.screenshot('token-validation-error');
            } else {
                cy.log('No validation result found');
                cy.screenshot('no-validation-result');
            }
        });
    }

    validateToken() {
        // Enter the token details
        this.enterTokenDetails();

        // Click the validate button
        this.clickValidateButton();

        // Check the validation result
        this.checkValidationResult();

        // Wait for any operations to complete
        cy.wait(3000);

        // Take a screenshot after token validation
        cy.screenshot('after-token-validation');
    }
}

export default ValidateTokenPage;