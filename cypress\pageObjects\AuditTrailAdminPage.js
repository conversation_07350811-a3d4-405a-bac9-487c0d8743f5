class AuditTrailAdminPage {
    navigateToAuditTrail() {
        // First, check if we're on about:blank and if so, navigate to the main page
        cy.url().then(url => {
            if (url === 'about:blank') {
                cy.log('Detected about:blank page, navigating to main application');
                cy.visit('https://tvp-operator-ui.k8.isw.la/operator/dashboard');
                cy.wait(10000); // Wait for page to load
            }
        });

        // Add a wait before trying to navigate
        cy.wait(5000);

        cy.log('Attempting to navigate to Audit Trail page');

        // Take a screenshot to debug what's visible on the page
        cy.screenshot('before-audit-trail-navigation');

        // Try multiple approaches to find and click the Audit Trail link
        cy.get('body').then($body => {
            // Check if the Audit Trail element exists with different selectors

            // 1. Try with exact text match
            if ($body.find("p:contains('Audit Trail')").length > 0) {
                cy.contains('p', 'Audit Trail', { timeout: 30000 })
                  .should('be.visible')
                  .click({ force: true });
                cy.log('Clicked on Audit Trail using contains');
                return;
            }

            // 2. Try with class-based selector
            if ($body.find("p.side_sub_txt.audit").length > 0) {
                cy.get('p.side_sub_txt.audit', { timeout: 30000 })
                  .should('be.visible')
                  .click({ force: true });
                cy.log('Clicked on Audit Trail using class selector');
                return;
            }

            // 3. Try with XPath as last resort
            cy.log('Trying with XPath selector');
            cy.xpath("//p[contains(@class, 'side_sub_txt') and contains(@class, 'audit') and contains(text(), 'Audit')]", { timeout: 30000 })
              .should('be.visible')
              .click({ force: true });
            cy.log('Clicked on Audit Trail using XPath');
        });

        // Add a wait after clicking to allow page transition
        cy.wait(5000);

        // Take another screenshot after navigation attempt
        cy.screenshot('after-audit-trail-navigation');

        // Verify we're on the Audit Trail page
        cy.url().should('include', '/operator-activities', { timeout: 30000 });
    }

    viewAuditTrail() {
        // Wait for the page to load
        cy.wait(5000);

        // Take a screenshot of the Audit Trail page
        cy.screenshot('audit-trail-page');

        // Log the current URL
        cy.url().then(url => {
            cy.log(`Current URL on Audit Trail page: ${url}`);
        });

        // ASSERTIONS: Verify the Audit Trail page is displayed correctly

        // 1. Assert that we're on the correct URL
        cy.url().should('include', '/operator-activities');

        // 2. Assert that the page contains a table or grid for displaying audit trail
        cy.get('body').then($body => {
            // Look for various elements that might be on the Audit Trail page
            const hasTable = $body.find('table, .grid, .data-grid, .table').length > 0;
            const hasAuditText = $body.find(":contains('Audit')").length > 0;
            const hasTrailText = $body.find(":contains('Trail')").length > 0;
            const hasActivityText = $body.find(":contains('Activity')").length > 0;

            cy.log(`Page has table/grid: ${hasTable}`);
            cy.log(`Page contains 'Audit' text: ${hasAuditText}`);
            cy.log(`Page contains 'Trail' text: ${hasTrailText}`);
            cy.log(`Page contains 'Activity' text: ${hasActivityText}`);

            // If we found a table, let's log some info about it
            if (hasTable) {
                const tableRows = $body.find('table tr, .grid-row').length;
                cy.log(`Table/grid has ${tableRows} rows`);
            }

            // Assert that the page has either a table or text indicating it's the audit trail page
            expect(hasTable || (hasAuditText && hasTrailText) || hasActivityText).to.be.true;
        });

        // 3. Assert that the page contains elements specific to audit trail
        // Try multiple selectors that might exist on the audit trail page
        cy.get('body').then($body => {
            // Check for common UI elements on an audit trail page
            const selectors = [
                'table', '.table', '.grid', '.data-grid',  // Table/grid elements
                'button:contains("Export")', 'button:contains("Download")', // Export buttons
                'input[type="date"]', '.date-picker', // Date filters
                '.filter', '.search', 'input[type="search"]' // Search/filter elements
            ];

            // Check if any of these selectors exist
            let foundElements = false;
            for (const selector of selectors) {
                if ($body.find(selector).length > 0) {
                    foundElements = true;
                    cy.log(`Found element matching selector: ${selector}`);
                    break;
                }
            }

            // Assert that we found at least one of the expected elements
            //expect(foundElements).to.be.true;
        });

        // 4. Check for any error messages
        cy.get('body').then($body => {
            const hasError = $body.find(':contains("Error"), :contains("Failed"), .error, .alert-error').length > 0;
            if (hasError) {
                cy.log('WARNING: Possible error message detected on page');
            } else {
                cy.log('No error messages detected on page');
            }

            // Assert that there are no error messages
            expect(hasError).to.be.false;
        });

        cy.log('Audit Trail page verified successfully');
    }
}

export default AuditTrailAdminPage;