class RolesAdminPage {
    navigateToRolesAdmin() {
        // First, check if we're on about:blank and if so, navigate to the main page
        cy.url().then(url => {
            if (url === 'about:blank') {
                cy.log('Detected about:blank page, navigating to main application');
                cy.visit('https://tvp-operator-ui.k8.isw.la/operator/dashboard');
                cy.wait(10000); // Wait for page to load
            }
        });

        // Add a wait before trying to navigate
        cy.wait(5000);

        cy.log('Attempting to navigate to Roles page');

        // Take a screenshot to debug what's visible on the page
        cy.screenshot('before-roles-navigation');

        // Try multiple approaches to find and click the Roles link
        cy.get('body').then($body => {
            // Check if the Roles element exists with different selectors

            // 1. Try with exact text match
            if ($body.find("p:contains('Roles')").length > 0) {
                cy.contains('p', 'Roles', { timeout: 30000 })
                  .should('be.visible')
                  .click({ force: true });
                cy.log('Clicked on Roles using contains');
                return;
            }

            // 2. Try with class-based selector
            if ($body.find("p.side_sub_txt.roles").length > 0) {
                cy.get('p.side_sub_txt.roles', { timeout: 30000 })
                  .should('be.visible')
                  .click({ force: true });
                cy.log('Clicked on Roles using class selector');
                return;
            }

            // 3. Try with XPath as last resort
            cy.log('Trying with XPath selector');
            cy.xpath("//p[contains(@class, 'side_sub_txt') and contains(@class, 'roles') and contains(text(), 'Roles')]", { timeout: 30000 })
              .should('be.visible')
              .click({ force: true });
            cy.log('Clicked on Roles using XPath');
        });

        // Add a wait after clicking to allow page transition
        cy.wait(5000);

        // Take another screenshot after navigation attempt
        cy.screenshot('after-roles-navigation');

        // Verify we're on the Roles page by URL
        cy.url().should('include', '/manage-roles', { timeout: 30000 });

        // Log page information for debugging
        cy.get('body').then($body => {
            // Look for various elements that might be on the Roles page
            const hasTable = $body.find('table, .grid, .data-grid, .table').length > 0;
            const hasRolesText = $body.find(":contains('Roles')").length > 0;

            cy.log(`Page has table/grid: ${hasTable}`);
            cy.log(`Page contains 'Roles' text: ${hasRolesText}`);

            // If we found a table, let's log some info about it
            if (hasTable) {
                const tableRows = $body.find('table tr, .grid-row').length;
                cy.log(`Table/grid has ${tableRows} rows`);
            }
        });
    }
}

export default RolesAdminPage;