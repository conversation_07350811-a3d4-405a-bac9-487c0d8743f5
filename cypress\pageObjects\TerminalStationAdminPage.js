class TerminalStationAdminPage {
    generateRandomString(length) {
        const characters = "abcdefghijklmnopqrstuvwxyz";
        let result = "";
        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return result;
    }

    // Navigate to Terminal/Stations menu
    navigateToTerminalStationAdmin() {
        // First, check if we're on about:blank and if so, navigate to the main page
        cy.url().then(url => {
            if (url === 'about:blank') {
                cy.log('Detected about:blank page, navigating to main application');
                cy.visit('https://tvp-operator-ui.k8.isw.la/operator/dashboard');
                cy.wait(10000); // Wait for page to load
            }
        });

        // Add a wait before trying to navigate
        cy.wait(5000);

        cy.log('Attempting to navigate to Terminal/Stations page');

        // Take a screenshot to debug what's visible on the page
        cy.screenshot('before-terminal-stations-navigation');

        // Try multiple approaches to find and click the Terminal/Stations link
        cy.get('body').then($body => {
            // Check if the Terminal/Stations element exists with different selectors

            // 1. Try with exact text match
            if ($body.find("p:contains('Terminal/Stations')").length > 0) {
                cy.contains('p', 'Terminal/Stations', { timeout: 30000 })
                  .should('be.visible')
                  .click({ force: true });
                cy.log('Clicked on Terminal/Stations using contains');
                return;
            }

            // 2. Try with class-based selector
            if ($body.find("p.side_sub_txt.stations").length > 0) {
                cy.get('p.side_sub_txt.stations', { timeout: 30000 })
                  .should('be.visible')
                  .click({ force: true });
                cy.log('Clicked on Terminal/Stations using class selector');
                return;
            }

            // 3. Try with XPath as last resort
            cy.log('Trying with XPath selector');
            cy.xpath("//p[contains(@class, 'side_sub_txt') and contains(@class, 'stations')]", { timeout: 30000 })
              .should('be.visible')
              .click({ force: true });
            cy.log('Clicked on Terminal/Stations using XPath');
        });

        // Add a wait after clicking to allow page transition
        cy.wait(5000);

        // Take another screenshot after navigation attempt
        cy.screenshot('after-terminal-stations-navigation');

        /// Verify we're on the Terminal/Stations page by URL
        //cy.url().should('include', '/terminal-stations', { timeout: 30000 });

        // Log page information for debugging
        cy.get('body').then($body => {
            // Look for various elements that might be on the Terminal/Stations page
            const hasTable = $body.find('table, .grid, .data-grid, .table').length > 0;
            const hasTerminalText = $body.find(":contains('Terminal')").length > 0;
            const hasStationsText = $body.find(":contains('Stations')").length > 0;

            cy.log(`Page has table/grid: ${hasTable}`);
            cy.log(`Page contains 'Terminal' text: ${hasTerminalText}`);
            cy.log(`Page contains 'Stations' text: ${hasStationsText}`);

            // If we found a table, let's log some info about it
            if (hasTable) {
                const tableRows = $body.find('table tr, .grid-row').length;
                cy.log(`Table/grid has ${tableRows} rows`);
            }
        });
    }

    clickCreateNewTerminalStation() {
        cy.xpath("//div[contains(@class,'primary_btn') and text()='Create New Terminal/Station']").click();
    }

    selectCountry() {
        // Click on the country dropdown
        cy.get('#select_flag_button').click();
        cy.wait(1000);

        // Click on Nigeria
        cy.xpath("//span[text()='Nigeria']").click({ force: true });

        // Wait for the country to be selected and the state dropdown to become available
        cy.wait(3000);

        // Log for debugging
        cy.log('Selected country: Nigeria');

        // Take a screenshot after country selection
        cy.screenshot('after-country-selection');
    }

    selectState() {
        // Take a screenshot before selecting state
        cy.screenshot('before-state-selection');

        // Step 1: Check if the state dropdown is already showing a value
        cy.get('body').then($body => {
            const stateDropdown = $body.find(".css-1uccc91-singleValue, .css-selected-value").eq(0);

            if (stateDropdown.length > 0 && stateDropdown.text().includes('Abia')) {
                cy.log('Abia state is already selected');
                return;
            }

            // If not already selected, proceed with selection
            cy.log('Selecting Abia state');

            // Click on the state dropdown
            cy.xpath("//div[contains(@class,'css-1wa3eu0-placeholder') and text()='Select State']")
              .click({ force: true });

            // Wait for the dropdown to open
            cy.wait(2000);

            // Take a screenshot of the open dropdown
            cy.screenshot('state-dropdown-open');

            // Try different approaches to select Abia

            // Approach 1: Try to find and click on the Abia option directly
            cy.get('body').then($body => {
                const abiaOption = $body.find(".css-v62nss-option:contains('Abia'), .css-option:contains('Abia'), [id*='react-select'][id*='option']:contains('Abia')");

                if (abiaOption.length > 0) {
                    cy.log('Found Abia option, clicking on it');

                    // Use XPath to click on the first option containing Abia
                    cy.xpath("//div[contains(@class, 'css-v62nss-option') and contains(text(), 'Abia')]")
                      .first()
                      .click({ force: true });
                } else {
                    cy.log('Abia option not found in dropdown, trying to type it');

                    // Try to find the input field
                    const inputField = $body.find("input[type='text'][aria-autocomplete='list']");

                    if (inputField.length > 0) {
                        // Type Abia and press enter
                        cy.wrap(inputField)
                          .first()
                          .clear()
                          .type('Abia{enter}', { force: true });

                        cy.log('Typed "Abia" into input field');
                    } else {
                        cy.log('Input field not found, trying with XPath');

                        // Try with XPath
                        cy.xpath("//input[@type='text' and @aria-autocomplete='list']")
                          .first()
                          .clear()
                          .type('Abia{enter}', { force: true });
                    }
                }
            });

            // Wait for the selection to be processed
            cy.wait(3000);
        });

        // Verify that Abia is selected
        cy.get('body').then($body => {
            const selectedState = $body.find(".css-1uccc91-singleValue, .css-selected-value").eq(0).text();
            cy.log(`Currently selected state: ${selectedState}`);

            if (selectedState.includes('Abia')) {
                cy.log('Abia state successfully selected');
            } else {
                cy.log('WARNING: Abia state not selected, test may fail');
            }
        });

        // Take a screenshot after state selection
        cy.screenshot('after-state-selection');

        // Wait for the local government dropdown to become available
        cy.wait(2000);
    }

    selectLocalGovernment() {
        // First, verify that a state is selected before proceeding
        cy.get('body').then($body => {
            const selectedState = $body.find(".css-1uccc91-singleValue, .css-selected-value").text();
            cy.log(`Currently selected state before LGA selection: ${selectedState}`);

            if (!selectedState.includes('Abia')) {
                cy.log('WARNING: Abia state not selected before attempting to select LGA');
                cy.screenshot('state-not-selected-before-lga');

                // Try to select the state again
                this.selectState();
                cy.wait(3000);
            }
        });

        // Take a screenshot before LGA selection
        cy.screenshot('before-lga-selection');

        // Step 1: Click on the local government dropdown with force: true to handle if it's covered
        cy.xpath("//div[contains(@class,'css-1wa3eu0-placeholder') and text()='Select Local Government']")
          .click({ force: true });

        // Step 2: Wait for the dropdown to open
        cy.wait(3000);

        // Step 3: Take a screenshot to see the dropdown
        cy.screenshot('lga-dropdown-open');

        // Step 4: Try a direct approach with XPath to select Aba North
        cy.xpath("//div[contains(@class, 'css-v62nss-option') and contains(text(), 'Aba North')]")
          .then($option => {
              if ($option.length > 0) {
                  cy.wrap($option).first().click({ force: true });
                  cy.log('Clicked on Aba North option using XPath');
              } else {
                  cy.log('Aba North option not found with XPath, trying alternative approaches');

                  // Try to find the input field and type directly
                  cy.get('input[type="text"][aria-autocomplete="list"]')
                    .then($input => {
                        if ($input.length > 0) {
                            cy.wrap($input).first().clear().type('Aba North', { force: true });
                            cy.wait(1000);
                            cy.wrap($input).first().type('{enter}', { force: true });
                            cy.log('Typed "Aba North" into input field');
                        } else {
                            // Try with a more generic approach
                            cy.log('Input field not found, trying with more generic selectors');

                            // Try clicking on the dropdown again to ensure it's open
                            cy.xpath("//div[contains(@class,'css-1wa3eu0-placeholder') and text()='Select Local Government']")
                              .click({ force: true });
                            cy.wait(2000);

                            // Try to find any visible input field
                            cy.get('input:visible').then($inputs => {
                                if ($inputs.length > 0) {
                                    cy.wrap($inputs).first().clear().type('Aba North{enter}', { force: true });
                                    cy.log('Typed "Aba North" into visible input field');
                                } else {
                                    cy.log('No visible input fields found');
                                }
                            });
                        }
                    });
              }
          });

        // Step 5: Add a longer wait to ensure the dropdown has time to process the selection
        cy.wait(5000);

        // Step 6: Verify that Aba North is selected
        cy.get('body').then($body => {
            const selectedLGA = $body.find(".css-1uccc91-singleValue, .css-selected-value").eq(1).text();
            cy.log(`Currently selected LGA: ${selectedLGA}`);

            if (selectedLGA.includes('Aba North')) {
                cy.log('Aba North successfully selected');
            } else {
                cy.log('WARNING: Aba North not selected, trying one more approach');

                // One more attempt with a different approach
                cy.xpath("//div[contains(@class,'css-1wa3eu0-placeholder') and text()='Select Local Government']")
                  .click({ force: true });
                cy.wait(2000);

                // Try to use keyboard navigation: type 'a' to jump to entries starting with 'a'
                cy.get('body').type('a', { force: true });
                cy.wait(1000);

                // Press down arrow to navigate to Aba North
                cy.get('body').type('{downarrow}', { force: true });
                cy.wait(500);
                cy.get('body').type('{downarrow}', { force: true });
                cy.wait(500);

                // Press enter to select
                cy.get('body').type('{enter}', { force: true });
                cy.wait(2000);
            }

            // Take a screenshot after LGA selection
            cy.screenshot('after-lga-selection');
        });

        // Step 7: Log for debugging
        cy.log('Attempted to select local government: Aba North');
    }

    enterTerminalName() {
        const terminalName = "Test" + this.generateRandomString(7);

        // Clear any existing value first
        cy.get('input[name="name"]').clear().type(terminalName, { force: true });

        // Verify the input has the value
        cy.get('input[name="name"]').should('have.value', terminalName);

        cy.log(`Entered terminal name: ${terminalName}`);
    }

    enterAddress() {
        // Clear any existing value first
        cy.xpath("//input[@placeholder='Search Address ...']").clear();

        // Type the address and press enter
        cy.xpath("//input[@placeholder='Search Address ...']").type('Lagos, Nigeria', { force: true });

        // Wait for address suggestions to appear
        cy.wait(2000);

        // Try to select the first suggestion if available
        cy.get('body').then($body => {
            const suggestions = $body.find('.suggestion-item, .address-suggestion, [role="option"]');

            if (suggestions.length > 0) {
                cy.log('Address suggestions found, selecting the first one');
                cy.get('.suggestion-item, .address-suggestion, [role="option"]').first().click({ force: true });
            } else {
                cy.log('No address suggestions found, pressing enter');
                cy.xpath("//input[@placeholder='Search Address ...']").type('{enter}', { force: true });
            }
        });

        // Wait for the address to be processed
        cy.wait(2000);

        cy.log('Entered address: Lagos, Nigeria');
    }

    clickAddTerminalStation() {
        // Click the Add Terminal/Station button with force: true to handle if it's covered
        cy.xpath("//button[@type='submit' and text()='Add Terminal/Station']")
          .click({ force: true });

        // Add a wait to ensure the form submission is processed
        cy.wait(2000);

        // Log for debugging
        cy.log('Clicked Add Terminal/Station button');
    }

    clickBackButton() {
        // Click the Back button with force: true to handle if it's covered
        cy.xpath("//p[contains(@class, 'back_txt') and text()='Back']")
          .click({ force: true });

        // Add a wait to ensure the navigation is processed
        cy.wait(2000);

        // Log for debugging
        cy.log('Clicked Back button');
    }

    // Combined method to create a terminal station
    createTerminalStation() {
        // Click the create new terminal station button
        this.clickCreateNewTerminalStation();

        // Wait for the form to load
        cy.wait(3000);

        // Take a screenshot before filling the form
        cy.screenshot('before-filling-terminal-form');

        // Fill in the form with proper waits between each step
        cy.log('Step 1: Selecting country');
        this.selectCountry();
        cy.wait(2000);

        cy.log('Step 2: Selecting state');
        this.selectState();
        cy.wait(2000);

        cy.log('Step 3: Selecting local government');
        this.selectLocalGovernment();
        cy.wait(2000);

        cy.log('Step 4: Entering terminal name');
        this.enterTerminalName();
        cy.wait(1000);

        cy.log('Step 5: Entering address');
        this.enterAddress();
        cy.wait(2000);

        // Check for any validation errors before submitting
        cy.get('body').then($body => {
            const hasError = $body.find('.error, .validation-error, .invalid-feedback, [style*="color: red"]').length > 0;

            if (hasError) {
                cy.log('WARNING: Validation errors detected before submission');
                cy.screenshot('validation-errors-before-submission');

                // Log the error messages
                cy.get('.error, .validation-error, .invalid-feedback, [style*="color: red"]').each(($el) => {
                    cy.log(`Validation error: ${$el.text()}`);
                });
            } else {
                cy.log('No validation errors detected, proceeding with submission');
            }
        });

        // Take a screenshot before submitting the form
        cy.screenshot('before-submitting-terminal-form');

        // Submit the form
        cy.log('Step 6: Submitting the form');
        this.clickAddTerminalStation();

        // Wait for submission to complete
        cy.wait(5000);

        // Check for success or error messages after submission
        cy.get('body').then($body => {
            const hasSuccess = $body.find('.success, .success-message, [style*="color: green"]').length > 0;
            const hasError = $body.find('.error, .error-message, .validation-error, [style*="color: red"]').length > 0;

            if (hasSuccess) {
                cy.log('SUCCESS: Terminal station created successfully');
                cy.screenshot('terminal-creation-success');
            } else if (hasError) {
                cy.log('ERROR: Failed to create terminal station');
                cy.screenshot('terminal-creation-error');

                // Log the error messages
                cy.get('.error, .error-message, .validation-error, [style*="color: red"]').each(($el) => {
                    cy.log(`Error message: ${$el.text()}`);
                });
            } else {
                cy.log('No success or error message found, checking URL for navigation');

                // Check if we've been redirected to a success page
                cy.url().then(url => {
                    cy.log(`Current URL after submission: ${url}`);
                });
            }
        });

        // Go back to the terminal stations list
        cy.log('Step 7: Going back to terminal stations list');
        this.clickBackButton();
    }
}

export default TerminalStationAdminPage;