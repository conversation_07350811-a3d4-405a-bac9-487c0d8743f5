class ClassAdminPage {
    generateRandomString(length) {
        const characters = "abcdefghijklmnopqrstuvwxyz";
        let result = "";
        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return result;
    }

    navigateToClassAdmin() {
        // First, check if we're on about:blank and if so, navigate to the main page
        cy.url().then(url => {
            if (url === 'about:blank') {
                cy.log('Detected about:blank page, navigating to main application');
                cy.visit('https://tvp-operator-ui.k8.isw.la/operator/dashboard');
                cy.wait(10000); // Wait for page to load
            }
        });

        // Add a wait before trying to navigate
        cy.wait(5000);

        cy.log('Attempting to navigate to Class page');

        // Take a screenshot to debug what's visible on the page
        cy.screenshot('before-class-navigation');

        // Try multiple approaches to find and click the Class link
        cy.get('body').then($body => {
            // Check if the Class element exists with different selectors

            // 1. Try with exact text match
            if ($body.find("p:contains('Class')").length > 0) {
                cy.contains('p', 'Class', { timeout: 30000 })
                  .should('be.visible')
                  .click({ force: true });
                cy.log('Clicked on Class using contains');
                return;
            }

            // 2. Try with class-based selector
            if ($body.find("p.side_sub_txt:contains('Class')").length > 0) {
                cy.get('p.side_sub_txt:contains("Class")', { timeout: 30000 })
                  .should('be.visible')
                  .click({ force: true });
                cy.log('Clicked on Class using class selector');
                return;
            }

            // 3. Try with XPath as last resort
            cy.log('Trying with XPath selector');
            cy.xpath("//p[contains(@class, 'side_sub_txt') and contains(text(), 'Class')]", { timeout: 30000 })
              .should('be.visible')
              .click({ force: true });
            cy.log('Clicked on Class using XPath');
        });

        // Add a wait after clicking to allow page transition
        cy.wait(5000);

        // Take another screenshot after navigation attempt
        cy.screenshot('after-class-navigation');

        // Verify we're on the Class page by URL
        //cy.url().should('include', '/class', { timeout: 30000 });

        // Log page information for debugging
        cy.get('body').then($body => {
            // Look for various elements that might be on the Class page
            const hasTable = $body.find('table, .grid, .data-grid, .table').length > 0;
            const hasClassText = $body.find(":contains('Class')").length > 0;

            cy.log(`Page has table/grid: ${hasTable}`);
            cy.log(`Page contains 'Class' text: ${hasClassText}`);

            // If we found a table, let's log some info about it
            if (hasTable) {
                const tableRows = $body.find('table tr, .grid-row').length;
                cy.log(`Table/grid has ${tableRows} rows`);
            }
        });
    }

    clickCreateNewClass() {
        // Click the Create New Classes button with force: true to handle if it's covered
        cy.xpath("//div[contains(@class,'primary_btn') and text()='Create New Classes']")
          .click({ force: true });

        // Add a wait to ensure the form loads
        cy.wait(2000);

        // Log for debugging
        cy.log('Clicked Create New Classes button');

        // Take a screenshot after clicking
        cy.screenshot('after-click-create-new-class');
    }

    enterClassName() {
        const className = "Test" + this.generateRandomString(4);

        // Clear any existing value first
        cy.get('input[name="name"]').clear().type(className, { force: true });

        // Verify the input has the value
        cy.get('input[name="name"]').should('have.value', className);

        cy.log(`Entered class name: ${className}`);
    }

    clickAddClass() {
        // Click the Add Class button with force: true to handle if it's covered
        cy.xpath("//button[@type='submit' and contains(@class,'primary_btn')]")
          .click({ force: true });

        // Add a wait to ensure the form submission is processed
        cy.wait(5000);

        // Check for success or error messages
        cy.get('body').then($body => {
            const hasSuccess = $body.find(":contains('Class created successfully')").length > 0;
            const hasError = $body.find(":contains('Error'), :contains('Failed'), .error, .alert-error").length > 0;

            if (hasSuccess) {
                cy.log('Class created successfully');
                cy.contains('Class created successfully').should('be.visible');
            } else if (hasError) {
                cy.log('Error creating class');
                cy.screenshot('class-creation-error');
            } else {
                cy.log('No success or error message found');
            }
        });

        // Take a screenshot after clicking
        cy.screenshot('after-click-add-class');
    }

    // Combined method to create a class
    createClass() {
        // Click the create new class button
        this.clickCreateNewClass();

        // Enter the class name
        this.enterClassName();

        // Submit the form
        this.clickAddClass();

        // Wait for any operations to complete
        cy.wait(3000);

        // Take a screenshot after class creation
        cy.screenshot('after-class-creation');
    }
}

export default ClassAdminPage;