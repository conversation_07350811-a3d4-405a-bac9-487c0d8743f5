import 'cypress-xpath';
import 'cypress-mochawesome-reporter/register';

// Log information about the current page
Cypress.Commands.add('logPageInfo', () => {
  cy.log('Logging page information for debugging');

  cy.url().then(url => {
    cy.log(`Current URL: ${url}`);
  });

  cy.document().then(doc => {
    cy.log(`Page title: ${doc.title}`);
  });

  cy.get('body').then($body => {
    // Log if specific elements exist
    const hasInsightsLink = $body.find(":contains('Insights')").length > 0;
    cy.log(`Page contains 'Insights' text: ${hasInsightsLink}`);

    // Log all visible links on the page
    const links = $body.find('a:visible');
    cy.log(`Number of visible links on page: ${links.length}`);

    // Log all menu items
    const menuItems = $body.find('li:visible, .menu-item, .nav-item');
    cy.log(`Number of potential menu items: ${menuItems.length}`);
  });
});

// Add a command to handle navigation errors
Cypress.on('fail', (error, _runnable) => {
  // Take a screenshot on failure
  cy.screenshot(`error-${Date.now()}`);

  // Log the error
  cy.log(`Test failed with error: ${error.message}`);

  // If it's a navigation error, we can try to recover
  if (error.message.includes('navigation') || error.message.includes('timed out')) {
    cy.log('Navigation error detected, attempting to recover');
    return false; // Return false to prevent the error from failing the test
  }

  throw error; // Re-throw the error for other types of failures
});

// Handle uncaught exceptions from the application
Cypress.on('uncaught:exception', (err, _runnable) => {
  // Log the error for debugging
  console.log('Uncaught exception:', err.message);

  // Prevent Cypress from failing the test when the application throws an error
  return false;
});