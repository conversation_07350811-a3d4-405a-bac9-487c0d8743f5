<?xml version="1.0" encoding="UTF-8"?>
<testng-results ignored="0" total="11" passed="11" failed="0" skipped="0">
  <reporter-output>
  </reporter-output>
  <suite started-at="2025-02-05T12:37:02 WAT" name="Suite" finished-at="2025-02-05T12:40:45 WAT" duration-ms="223650">
    <groups>
    </groups>
    <test started-at="2025-02-05T12:37:02 WAT" name="Test" finished-at="2025-02-05T12:40:45 WAT" duration-ms="223650">
      <class name="testscript.TestScript">
        <test-method is-config="true" signature="setUp()[pri:0, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:37:02 WAT" name="setUp" finished-at="2025-02-05T12:37:18 WAT" duration-ms="15916" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testLogin()[pri:1, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:37:18 WAT" name="testLogin" finished-at="2025-02-05T12:37:34 WAT" duration-ms="15786" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testLogin -->
        <test-method signature="testNavigateToInsights()[pri:2, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:37:34 WAT" name="testNavigateToInsights" finished-at="2025-02-05T12:37:39 WAT" duration-ms="5439" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testNavigateToInsights -->
        <test-method signature="testDownloadReports()[pri:3, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:37:39 WAT" name="testDownloadReports" finished-at="2025-02-05T12:37:50 WAT" duration-ms="10914" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testDownloadReports -->
        <test-method signature="testTokenReport()[pri:4, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:37:50 WAT" name="testTokenReport" finished-at="2025-02-05T12:38:10 WAT" duration-ms="20381" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testTokenReport -->
        <test-method signature="testUserCreation()[pri:5, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:38:10 WAT" name="testUserCreation" finished-at="2025-02-05T12:38:52 WAT" duration-ms="41357" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUserCreation -->
        <test-method signature="testAuditTrailNavigation()[pri:6, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:38:52 WAT" name="testAuditTrailNavigation" finished-at="2025-02-05T12:38:59 WAT" duration-ms="7216" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testAuditTrailNavigation -->
        <test-method signature="testRolesNavigation()[pri:7, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:38:59 WAT" name="testRolesNavigation" finished-at="2025-02-05T12:39:04 WAT" duration-ms="5210" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testRolesNavigation -->
        <test-method signature="testTerminalCreation()[pri:8, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:39:04 WAT" name="testTerminalCreation" finished-at="2025-02-05T12:39:45 WAT" duration-ms="40989" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testTerminalCreation -->
        <test-method signature="testClassCreation()[pri:9, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:39:45 WAT" name="testClassCreation" finished-at="2025-02-05T12:39:55 WAT" duration-ms="9141" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testClassCreation -->
        <test-method signature="testValidateToken()[pri:10, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:39:55 WAT" name="testValidateToken" finished-at="2025-02-05T12:40:00 WAT" duration-ms="5864" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testValidateToken -->
        <test-method signature="testRedeemToken()[pri:11, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:40:00 WAT" name="testRedeemToken" finished-at="2025-02-05T12:40:32 WAT" duration-ms="31499" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testRedeemToken -->
        <test-method is-config="true" signature="tearDown()[pri:0, instance:testscript.TestScript@bcec361]" started-at="2025-02-05T12:40:32 WAT" name="tearDown" finished-at="2025-02-05T12:40:45 WAT" duration-ms="13467" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- tearDown -->
      </class> <!-- testscript.TestScript -->
    </test> <!-- Test -->
  </suite> <!-- Suite -->
</testng-results>
