import LoginPage from '../pageObjects/LoginPage';
import InsightsPage from '../pageObjects/InsightsPage';
import DownloadReportsPage from '../pageObjects/DownloadReportsPage';
import TokenReportPage from '../pageObjects/TokenReportPage';
import UserAdminPage from '../pageObjects/UserAdminPage';
import AuditTrailAdminPage from '../pageObjects/AuditTrailAdminPage';
import RolesAdminPage from '../pageObjects/RolesAdminPage';
import TerminalStationAdminPage from '../pageObjects/TerminalStationAdminPage';
import ClassAdminPage from '../pageObjects/ClassAdminPage';
import ValidateTokenPage from '../pageObjects/ValidateTokenPage';
import RedeemTokenPage from '../pageObjects/RedeemTokenPage';

describe('Operator UI E2E Tests', () => {
    const loginPage = new LoginPage();
    const insightsPage = new InsightsPage();
    const downloadReportsPage = new DownloadReportsPage();
    const tokenReportPage = new TokenReportPage();
    const userAdminPage = new UserAdminPage();
    const auditTrailAdminPage = new AuditTrailAdminPage();
    const rolesAdminPage = new RolesAdminPage();
    const terminalStationAdminPage = new TerminalStationAdminPage();
    const classAdminPage = new ClassAdminPage();
    const validateTokenPage = new ValidateTokenPage();
    const redeemTokenPage = new RedeemTokenPage();

    before(() => {
        loginPage.visit();
    });

    // Add a beforeEach hook to ensure we're logged in before each test
    beforeEach(() => {
        // Check if we need to log in
        cy.url().then(url => {
            // If we're on the login page or about:blank, we need to log in
            if (url === 'about:blank' || url.includes('/auth')) {
                cy.log('Not logged in, logging in first');

                // If we're not already on the login page, go there
                if (!url.includes('/auth')) {
                    loginPage.visit();
                }

                // Login
                loginPage.enterEmail('<EMAIL>');
                loginPage.enterPassword('Qwertyuiop@123');
                loginPage.clickLogin();

                // Wait for dashboard to load and verify we're logged in
                cy.url().should('include', '/dashboard', { timeout: 30000 });
                cy.log('Successfully logged in');
            } else {
                cy.log('Already logged in, continuing with test');
            }
        });
    });

    it('should navigate to Insights', () => {
        // Verify we're on the dashboard
        cy.url().should('include', '/dashboard', { timeout: 30000 });

        // Check if we can see the Insights menu item
        cy.get('body').then($body => {
            const hasInsights = $body.find('p.side_sub_txt.insights').length > 0;
            cy.log(`Insights menu item visible: ${hasInsights}`);
        });

        // Take a screenshot before navigation
        cy.screenshot('before-insights-navigation');

        // Navigate to Insights
        insightsPage.navigateToInsights();

        // Verify we're on the Insights page
        cy.url().should('include', '/insights', { timeout: 30000 });
        cy.log('Successfully navigated to Insights page');

        // Take a screenshot after navigation
        cy.screenshot('after-insights-navigation');
    });

    it('should navigate to Download Reports and download a report', () => {
        // Take a screenshot before navigation
        cy.screenshot('before-download-reports-test');

        // Verify we can see menu items before proceeding
        cy.get('p.side_sub_txt', { timeout: 30000 }).should('be.visible');

        // Log information about menu items
        cy.get('p.side_sub_txt').then($menuItems => {
            cy.log(`Found ${$menuItems.length} menu items with class 'side_sub_txt'`);

            // Log the text of each menu item
            $menuItems.each((index, element) => {
                cy.log(`Menu item ${index + 1}: ${Cypress.$(element).text()}`);
            });
        });

        // Navigate to Download Reports
        cy.log('Navigating to Download Reports');
        downloadReportsPage.navigateToDownloadReports();

        // Verify we're on the Download Reports page or a related page
        cy.url().then(url => {
            cy.log(`URL after navigation: ${url}`);
        });

        // Take a screenshot after navigation
        cy.screenshot('after-download-reports-navigation');

        // Try to download a report
        cy.log('Attempting to download report');
        downloadReportsPage.downloadReport();

        // Wait for download to complete
        cy.wait(10000);

        // Take a screenshot after download
        cy.screenshot('after-download-report-test');
    });

    it('should navigate to Token Report and view reports', () => {
        // Take a screenshot before navigation
        cy.screenshot('before-token-report-test');

        // Log the current URL
        cy.url().then(url => {
            cy.log(`Current URL before navigation: ${url}`);
        });

        // Navigate to Token Report
        cy.log('Navigating to Token Report');
        tokenReportPage.navigateToTokenReport();

        // Verify we're on the Token Report page
        cy.url().should('include', '/token-report', { timeout: 30000 });
        cy.log('Successfully navigated to Token Report page');

        // View the token reports
        tokenReportPage.viewTokenReport();

        // Wait for any data to load
        cy.wait(10000);

        // Take a screenshot after viewing reports
        cy.screenshot('after-token-report-test');
    });

    it('should manage users', () => {
        userAdminPage.navigateToUserAdmin();
        userAdminPage.createUser();
        cy.wait(5000);
    });

    it('should view audit trail', () => {
        auditTrailAdminPage.navigateToAuditTrail();
        auditTrailAdminPage.viewAuditTrail();
        cy.wait(5000);
    });

    it('should manage roles', () => {
        // Navigate to Roles Admin
        rolesAdminPage.navigateToRolesAdmin();

        // Wait for any operations to complete
        cy.wait(5000);
    });

    it('should manage terminal stations', () => {
        terminalStationAdminPage.navigateToTerminalStationAdmin();
        terminalStationAdminPage.createTerminalStation();

        cy.wait(5000);
    });

    it('should manage classes', () => {
        classAdminPage.navigateToClassAdmin();
        classAdminPage.createClass();

        cy.wait(5000);
    });

    it('should validate token', () => {
        validateTokenPage.navigateToValidateToken();
        validateTokenPage.validateToken();
        cy.wait(5000);
    });

    it('should redeem token', () => {
        redeemTokenPage.navigateToRedeemToken();
        redeemTokenPage.redeemToken();
        cy.wait(5000);
    });
});