const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    baseUrl: 'https://tvp-operator-ui.k8.isw.la/operator/auth',
    supportFile: 'cypress/support/e2e.js',
    specPattern: 'cypress/e2e/**/*.cy.js',
    defaultCommandTimeout: 30000,
    pageLoadTimeout: 120000,
    requestTimeout: 30000,
    responseTimeout: 60000,
    retries: {
      runMode: 2,
      openMode: 1
    },
    screenshotOnRunFailure: true,
    video: true,
    viewportWidth: 1280,
    viewportHeight: 720,
    testIsolation: false,  // Disable test isolation to maintain session between tests
    experimentalSessionAndOrigin: true,  // Enable experimental session support

    // Configure Mochawesome reporter
    reporter: 'cypress-mochawesome-reporter',
    reporterOptions: {
      charts: true,
      reportPageTitle: 'TVP Operator UI Tests',
      embeddedScreenshots: true,
      inlineAssets: true,
      saveAllAttempts: false
    },

    // Configure video and screenshot storage
    screenshotsFolder: 'cypress/reports/screenshots',
    videosFolder: 'cypress/reports/videos',

    // Setup function to register plugins
    setupNodeEvents(on, config) {
      // Implement node event listeners here
      require('cypress-mochawesome-reporter/plugin')(on);
      return config;
    }
  }
});