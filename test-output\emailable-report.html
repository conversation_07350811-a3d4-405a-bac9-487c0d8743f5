<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "https://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
<title>TestNG Report</title>
<style type="text/css">table {margin-bottom:10px;border-collapse:collapse;empty-cells:show}th,td {border:1px solid #009;padding:.25em .5em}th {vertical-align:bottom}td {vertical-align:top}table a {font-weight:bold}.stripe td {background-color: #E6EBF9}.num {text-align:right}.passedodd td {background-color: #3F3}.passedeven td {background-color: #0A0}.skippedodd td {background-color: #DDD}.skippedeven td {background-color: #CCC}.failedodd td,.attn {background-color: #F33}.failedeven td,.stripe .attn {background-color: #D00}.stacktrace {white-space:pre;font-family:monospace}.totop {font-size:85%;text-align:center;border-bottom:2px solid #000}.invisible {display:none}</style>
</head>
<body>
<table>
<tr><th>Test</th><th># Passed</th><th># Skipped</th><th># Retried</th><th># Failed</th><th>Time (ms)</th><th>Included Groups</th><th>Excluded Groups</th></tr>
<tr><th colspan="8">Suite</th></tr>
<tr><td><a href="#t0">Test</a></td><td class="num">11</td><td class="num">0</td><td class="num">0</td><td class="num">0</td><td class="num">223,650</td><td></td><td></td></tr>
</table>
<table id='summary'><thead><tr><th>Class</th><th>Method</th><th>Start</th><th>Time (ms)</th></tr></thead><tbody><tr><th colspan="4">Suite</th></tr></tbody><tbody id="t0"><tr><th colspan="4">Test &#8212; passed</th></tr><tr class="passedeven"><td rowspan="11">testscript.TestScript</td><td><a href="#m0">testAuditTrailNavigation</a></td><td rowspan="1">1738755532367</td><td rowspan="1">7216</td></tr><tr class="passedeven"><td><a href="#m1">testClassCreation</a></td><td rowspan="1">1738755585915</td><td rowspan="1">9141</td></tr><tr class="passedeven"><td><a href="#m2">testDownloadReports</a></td><td rowspan="1">1738755459659</td><td rowspan="1">10914</td></tr><tr class="passedeven"><td><a href="#m3">testLogin</a></td><td rowspan="1">1738755438310</td><td rowspan="1">15786</td></tr><tr class="passedeven"><td><a href="#m4">testNavigateToInsights</a></td><td rowspan="1">1738755454200</td><td rowspan="1">5439</td></tr><tr class="passedeven"><td><a href="#m5">testRedeemToken</a></td><td rowspan="1">1738755600977</td><td rowspan="1">31499</td></tr><tr class="passedeven"><td><a href="#m6">testRolesNavigation</a></td><td rowspan="1">1738755539596</td><td rowspan="1">5210</td></tr><tr class="passedeven"><td><a href="#m7">testTerminalCreation</a></td><td rowspan="1">1738755544911</td><td rowspan="1">40989</td></tr><tr class="passedeven"><td><a href="#m8">testTokenReport</a></td><td rowspan="1">1738755470579</td><td rowspan="1">20381</td></tr><tr class="passedeven"><td><a href="#m9">testUserCreation</a></td><td rowspan="1">1738755490967</td><td rowspan="1">41357</td></tr><tr class="passedeven"><td><a href="#m10">testValidateToken</a></td><td rowspan="1">1738755595089</td><td rowspan="1">5864</td></tr></tbody>
</table>
<h2>Test</h2><h3 id="m0">testscript.TestScript#testAuditTrailNavigation</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m1">testscript.TestScript#testClassCreation</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m2">testscript.TestScript#testDownloadReports</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m3">testscript.TestScript#testLogin</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m4">testscript.TestScript#testNavigateToInsights</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m5">testscript.TestScript#testRedeemToken</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m6">testscript.TestScript#testRolesNavigation</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m7">testscript.TestScript#testTerminalCreation</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m8">testscript.TestScript#testTokenReport</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m9">testscript.TestScript#testUserCreation</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m10">testscript.TestScript#testValidateToken</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
</body>
</html>
