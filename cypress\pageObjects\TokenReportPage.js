class TokenReportPage {
    navigateToTokenReport() {
        // First, check if we're on about:blank and if so, navigate to the main page
        cy.url().then(url => {
            if (url === 'about:blank') {
                cy.log('Detected about:blank page, navigating to main application');
                cy.visit('https://tvp-operator-ui.k8.isw.la/operator/dashboard');
                cy.wait(10000); // Wait for page to load
            }
        });

        // Add a wait before trying to navigate
        cy.wait(5000);

        cy.log('Attempting to navigate to Token Report page');

        // Take a screenshot to debug what's visible on the page
        cy.screenshot('before-token-report-navigation');

        // Try multiple approaches to find and click the Token Report link
        cy.get('body').then($body => {
            // Check if the Token Report element exists with different selectors

            // 1. Try with exact text match
            if ($body.find("p:contains('Token Report')").length > 0) {
                cy.contains('p', 'Token Report', { timeout: 30000 })
                  .should('be.visible')
                  .click({ force: true });
                cy.log('Clicked on Token Report using contains');
                return;
            }

            // 2. Try with class-based selector (assuming similar class structure to Insights)
            if ($body.find("p.side_sub_txt:contains('Token')").length > 0) {
                cy.get('p.side_sub_txt').contains('Token', { timeout: 30000 })
                  .should('be.visible')
                  .click({ force: true });
                cy.log('Clicked on Token Report using class selector');
                return;
            }

            // 3. Try with XPath as last resort
            cy.log('Trying with XPath selector');
            cy.xpath("//p[contains(text(), 'Token Report') or contains(text(), 'Token')]", { timeout: 30000 })
              .should('be.visible')
              .click({ force: true });
            cy.log('Clicked on Token Report using XPath');
        });

        // Add a wait after clicking to allow page transition
        cy.wait(5000);

        // Take another screenshot after navigation attempt
        cy.screenshot('after-token-report-navigation');

        // Verify we're on the Token Report page
        cy.url().should('include', '/token-report', { timeout: 30000 });
    }

    viewTokenReport() {
        // Wait for the page to load
        cy.wait(5000);

        // Take a screenshot of the Token Report page
        cy.screenshot('token-report-page');

        // Log the current URL
        cy.url().then(url => {
            cy.log(`Current URL on Token Report page: ${url}`);
        });

        // ASSERTIONS: Verify the Token Report page is displayed correctly

        // 1. Assert that we're on the correct URL
        cy.url().should('include', '/token-report');

        // 2. Assert that the page contains a table or grid for displaying token reports
        cy.get('body').then($body => {
            // Look for various elements that might be on the Token Report page
            const hasTable = $body.find('table, .grid, .data-grid, .table').length > 0;
            const hasTokenText = $body.find(":contains('Token')").length > 0;
            const hasReportText = $body.find(":contains('Report')").length > 0;

            cy.log(`Page has table/grid: ${hasTable}`);
            cy.log(`Page contains 'Token' text: ${hasTokenText}`);
            cy.log(`Page contains 'Report' text: ${hasReportText}`);

            // If we found a table, let's log some info about it
            if (hasTable) {
                const tableRows = $body.find('table tr, .grid-row').length;
                cy.log(`Table/grid has ${tableRows} rows`);
            }

            // Assert that the page has either a table or text indicating it's the token report page
            expect(hasTable || (hasTokenText && hasReportText)).to.be.true;
        });

        // 3. Assert that the page contains elements specific to token reports
        // Try multiple selectors that might exist on the token report page
        cy.get('body').then($body => {
            // Check for common UI elements on a report page
            const selectors = [
                'table', '.table', '.grid', '.data-grid',  // Table/grid elements
                'button:contains("Export")', 'button:contains("Download")', // Export buttons
                'input[type="date"]', '.date-picker', // Date filters
                '.filter', '.search', 'input[type="search"]' // Search/filter elements
            ];

            // Check if any of these selectors exist
            let foundElements = false;
            for (const selector of selectors) {
                if ($body.find(selector).length > 0) {
                    foundElements = true;
                    cy.log(`Found element matching selector: ${selector}`);
                    break;
                }
            }

            // Assert that we found at least one of the expected elements
            //expect(foundElements).to.be.true;
        });

        // 4. Assert that the page contains text related to token reports
        cy.contains(/Token|Report/).should('be.visible');

        // 5. Check for any error messages
        cy.get('body').then($body => {
            const hasError = $body.find(':contains("Error"), :contains("Failed"), .error, .alert-error').length > 0;
            if (hasError) {
                cy.log('WARNING: Possible error message detected on page');
            } else {
                cy.log('No error messages detected on page');
            }

            // Assert that there are no error messages
            expect(hasError).to.be.false;
        });

        cy.log('Token Report page verified successfully');
    }
}

export default TokenReportPage;