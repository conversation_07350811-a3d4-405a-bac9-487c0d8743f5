class InsightsPage {
    navigateToInsights() {
        // First, check if we're on about:blank and if so, navigate to the main page
        cy.url().then(url => {
            if (url === 'about:blank') {
                cy.log('Detected about:blank page, navigating to main application');
                cy.visit('https://tvp-operator-ui.k8.isw.la/operator/dashboard');
                cy.wait(10000); // Wait for page to load
            }
        });

        // Add a longer wait before trying to navigate to ensure dashboard is fully loaded
        cy.wait(5000);

        cy.log('Attempting to navigate to Insights page');

        // Take a screenshot to debug what's visible on the page
        cy.screenshot('before-insights-navigation');

        // Use the exact selector for the Insights element
        cy.get('p.side_sub_txt.insights.svg_ico.activities_ico', { timeout: 30000 })
            .should('be.visible')
            .should('contain.text', 'Insights')
            .click({ force: true });

        cy.log('Clicked on Insights using exact class selector');

        // Add a wait after clicking to allow page transition
        cy.wait(5000);

        // Take another screenshot after navigation attempt
        cy.screenshot('after-insights-navigation');

        // Verify we're on the Insights page
        cy.url().should('include', '/insights', { timeout: 30000 });
    }
}

export default InsightsPage;