image: cypress/browsers:node18.12.0-chrome106-ff106

definitions:
  caches:
    npm: ~/.npm
    cypress: ~/.cache/Cypress

pipelines:
  default:
    - step:
        name: Install dependencies
        caches:
          - npm
          - cypress
        script:
          - npm ci
          - npx cypress verify
        artifacts:
          - node_modules/**
          - ~/.cache/Cypress/**
    - step:
        name: Run Cypress tests
        caches:
          - npm
          - cypress
        script:
          - npx cypress run --browser chrome --headless
        artifacts:
          - cypress/reports/**
          - cypress/videos/**
          - cypress/screenshots/**
          - mochawesome-report/**

  branches:
    master:
      - step:
          name: Install dependencies
          caches:
            - npm
            - cypress
          script:
            - npm ci
            - npx cypress verify
          artifacts:
            - node_modules/**
            - ~/.cache/Cypress/**
      - step:
          name: Run Cypress tests
          caches:
            - npm
            - cypress
          script:
            - npx cypress run --browser chrome --headless
          artifacts:
            - cypress/reports/**
            - cypress/videos/**
            - cypress/screenshots/**
            - mochawesome-report/**