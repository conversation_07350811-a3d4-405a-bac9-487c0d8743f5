<!DOCTYPE html>

<html>
  <head>
  <meta charset='utf-8'>
  <title>TestNG reports</title>

    <link type="text/css" href="testng-reports1.css" rel="stylesheet" id="ultra" />
    <link type="text/css" href="testng-reports.css" rel="stylesheet" id="retro" disabled="false"/>
    <script type="text/javascript" src="jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="testng-reports.js"></script>
    <script type="text/javascript" src="https://www.google.com/jsapi"></script>
    <script type='text/javascript'>
      google.load('visualization', '1', {packages:['table']});
      google.setOnLoadCallback(drawTable);
      var suiteTableInitFunctions = new Array();
      var suiteTableData = new Array();
    </script>
    <!--
      <script type="text/javascript" src="jquery-ui/js/jquery-ui-1.8.16.custom.min.js"></script>
     -->
  </head>

  <body>    <div class="top-banner-root">
      <span class="top-banner-title-font">Test results</span>
      <button id="button" class="button">Switch Retro Theme</button> <!-- button -->
      <br/>
      <span class="top-banner-font-1">1 suite</span>
    </div> <!-- top-banner-root -->
    <div class="navigator-root">
      <div class="navigator-suite-header">
        <span>All suites</span>
        <a href="#" title="Collapse/expand all the suites" class="collapse-all-link">
          <img src="collapseall.gif" class="collapse-all-icon">
          </img> <!-- collapse-all-icon -->
        </a> <!-- collapse-all-link -->
      </div> <!-- navigator-suite-header -->
      <div class="suite">
        <div class="rounded-window">
          <div class="suite-header light-rounded-window-top">
            <a href="#" panel-name="suite-Suite" class="navigator-link">
              <span class="suite-name border-passed">Suite</span>
            </a> <!-- navigator-link -->
          </div> <!-- suite-header light-rounded-window-top -->
          <div class="navigator-suite-content">
            <div class="suite-section-title">
              <span>Info</span>
            </div> <!-- suite-section-title -->
            <div class="suite-section-content">
              <ul>
                <li>
                  <a href="#" panel-name="test-xml-Suite" class="navigator-link ">
                    <span>C:\Users\<USER>\Desktop\maven\src\test\resources\testng.xml</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="testlist-Suite" class="navigator-link ">
                    <span class="test-stats">1 test</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="group-Suite" class="navigator-link ">
                    <span>0 groups</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="times-Suite" class="navigator-link ">
                    <span>Times</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="reporter-Suite" class="navigator-link ">
                    <span>Reporter output</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="ignored-methods-Suite" class="navigator-link ">
                    <span>Ignored methods</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="chronological-Suite" class="navigator-link ">
                    <span>Chronological view</span>
                  </a> <!-- navigator-link  -->
                </li>
              </ul>
            </div> <!-- suite-section-content -->
            <div class="result-section">
              <div class="suite-section-title">
                <span>Results</span>
              </div> <!-- suite-section-title -->
              <div class="suite-section-content">
                <ul>
                  <li>
                    <span class="method-stats">11 methods,   11 passed</span>
                  </li>
                  <li>
                    <span class="method-list-title passed">Passed methods</span>
                    <span class="show-or-hide-methods passed">
                      <a href="#" panel-name="suite-Suite" class="hide-methods passed suite-Suite"> (hide)</a> <!-- hide-methods passed suite-Suite -->
                      <a href="#" panel-name="suite-Suite" class="show-methods passed suite-Suite"> (show)</a> <!-- show-methods passed suite-Suite -->
                    </span>
                    <div class="method-list-content passed suite-Suite">
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Suite" title="testscript.TestScript" class="method navigator-link" hash-for-method="testAuditTrailNavigation">testAuditTrailNavigation</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Suite" title="testscript.TestScript" class="method navigator-link" hash-for-method="testClassCreation">testClassCreation</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Suite" title="testscript.TestScript" class="method navigator-link" hash-for-method="testDownloadReports">testDownloadReports</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Suite" title="testscript.TestScript" class="method navigator-link" hash-for-method="testLogin">testLogin</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Suite" title="testscript.TestScript" class="method navigator-link" hash-for-method="testNavigateToInsights">testNavigateToInsights</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Suite" title="testscript.TestScript" class="method navigator-link" hash-for-method="testRedeemToken">testRedeemToken</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Suite" title="testscript.TestScript" class="method navigator-link" hash-for-method="testRolesNavigation">testRolesNavigation</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Suite" title="testscript.TestScript" class="method navigator-link" hash-for-method="testTerminalCreation">testTerminalCreation</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Suite" title="testscript.TestScript" class="method navigator-link" hash-for-method="testTokenReport">testTokenReport</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Suite" title="testscript.TestScript" class="method navigator-link" hash-for-method="testUserCreation">testUserCreation</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Suite" title="testscript.TestScript" class="method navigator-link" hash-for-method="testValidateToken">testValidateToken</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content passed suite-Suite -->
                  </li>
                </ul>
              </div> <!-- suite-section-content -->
            </div> <!-- result-section -->
          </div> <!-- navigator-suite-content -->
        </div> <!-- rounded-window -->
      </div> <!-- suite -->
    </div> <!-- navigator-root -->
    <div class="wrapper">
      <div class="main-panel-root">
        <div panel-name="suite-Suite" class="panel Suite">
          <div class="suite-Suite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">testscript.TestScript</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="testAuditTrailNavigation">
                  </a> <!-- testAuditTrailNavigation -->
                  <span class="method-name">testAuditTrailNavigation</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testClassCreation">
                  </a> <!-- testClassCreation -->
                  <span class="method-name">testClassCreation</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testDownloadReports">
                  </a> <!-- testDownloadReports -->
                  <span class="method-name">testDownloadReports</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testLogin">
                  </a> <!-- testLogin -->
                  <span class="method-name">testLogin</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testNavigateToInsights">
                  </a> <!-- testNavigateToInsights -->
                  <span class="method-name">testNavigateToInsights</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testRedeemToken">
                  </a> <!-- testRedeemToken -->
                  <span class="method-name">testRedeemToken</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testRolesNavigation">
                  </a> <!-- testRolesNavigation -->
                  <span class="method-name">testRolesNavigation</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testTerminalCreation">
                  </a> <!-- testTerminalCreation -->
                  <span class="method-name">testTerminalCreation</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testTokenReport">
                  </a> <!-- testTokenReport -->
                  <span class="method-name">testTokenReport</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testUserCreation">
                  </a> <!-- testUserCreation -->
                  <span class="method-name">testUserCreation</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testValidateToken">
                  </a> <!-- testValidateToken -->
                  <span class="method-name">testValidateToken</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Suite-class-passed -->
        </div> <!-- panel Suite -->
        <div panel-name="test-xml-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">C:\Users\<USER>\Desktop\maven\src\test\resources\testng.xml</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <pre>
&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
&lt;!DOCTYPE suite SYSTEM &quot;https://testng.org/testng-1.0.dtd&quot;&gt;
&lt;suite name=&quot;Suite&quot; guice-stage=&quot;DEVELOPMENT&quot;&gt;
  &lt;test thread-count=&quot;5&quot; name=&quot;Test&quot;&gt;
    &lt;classes&gt;
      &lt;class name=&quot;testscript.TestScript&quot;/&gt;
    &lt;/classes&gt;
  &lt;/test&gt; &lt;!-- Test --&gt;
&lt;/suite&gt; &lt;!-- Suite --&gt;
            </pre>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="testlist-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Tests for Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <ul>
              <li>
                <span class="test-name">Test (1 class)</span>
              </li>
            </ul>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="group-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Groups for Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="times-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Times for Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="times-div">
              <script type="text/javascript">
suiteTableInitFunctions.push('tableData_Suite');
function tableData_Suite() {
var data = new google.visualization.DataTable();
data.addColumn('number', 'Number');
data.addColumn('string', 'Method');
data.addColumn('string', 'Class');
data.addColumn('number', 'Time (ms)');
data.addRows(11);
data.setCell(0, 0, 0)
data.setCell(0, 1, 'testUserCreation')
data.setCell(0, 2, 'testscript.TestScript')
data.setCell(0, 3, 41357);
data.setCell(1, 0, 1)
data.setCell(1, 1, 'testTerminalCreation')
data.setCell(1, 2, 'testscript.TestScript')
data.setCell(1, 3, 40989);
data.setCell(2, 0, 2)
data.setCell(2, 1, 'testRedeemToken')
data.setCell(2, 2, 'testscript.TestScript')
data.setCell(2, 3, 31499);
data.setCell(3, 0, 3)
data.setCell(3, 1, 'testTokenReport')
data.setCell(3, 2, 'testscript.TestScript')
data.setCell(3, 3, 20381);
data.setCell(4, 0, 4)
data.setCell(4, 1, 'testLogin')
data.setCell(4, 2, 'testscript.TestScript')
data.setCell(4, 3, 15786);
data.setCell(5, 0, 5)
data.setCell(5, 1, 'testDownloadReports')
data.setCell(5, 2, 'testscript.TestScript')
data.setCell(5, 3, 10914);
data.setCell(6, 0, 6)
data.setCell(6, 1, 'testClassCreation')
data.setCell(6, 2, 'testscript.TestScript')
data.setCell(6, 3, 9141);
data.setCell(7, 0, 7)
data.setCell(7, 1, 'testAuditTrailNavigation')
data.setCell(7, 2, 'testscript.TestScript')
data.setCell(7, 3, 7216);
data.setCell(8, 0, 8)
data.setCell(8, 1, 'testValidateToken')
data.setCell(8, 2, 'testscript.TestScript')
data.setCell(8, 3, 5864);
data.setCell(9, 0, 9)
data.setCell(9, 1, 'testNavigateToInsights')
data.setCell(9, 2, 'testscript.TestScript')
data.setCell(9, 3, 5439);
data.setCell(10, 0, 10)
data.setCell(10, 1, 'testRolesNavigation')
data.setCell(10, 2, 'testscript.TestScript')
data.setCell(10, 3, 5210);
window.suiteTableData['Suite']= { tableData: data, tableDiv: 'times-div-Suite'}
return data;
}
              </script>
              <span class="suite-total-time">Total running time: 3 minutes</span>
              <div id="times-div-Suite">
              </div> <!-- times-div-Suite -->
            </div> <!-- times-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="reporter-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Reporter output for Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="ignored-methods-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">0 ignored methods</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="chronological-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Methods in chronological order</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="chronological-class">
              <div class="chronological-class-name">testscript.TestScript</div> <!-- chronological-class-name -->
              <div class="configuration-class before">
                <span class="method-name">setUp</span>
                <span class="method-start">0 ms</span>
              </div> <!-- configuration-class before -->
              <div class="test-method">
                <span class="method-name">testLogin</span>
                <span class="method-start">15929 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testNavigateToInsights</span>
                <span class="method-start">31819 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testDownloadReports</span>
                <span class="method-start">37278 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testTokenReport</span>
                <span class="method-start">48198 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testUserCreation</span>
                <span class="method-start">68586 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testAuditTrailNavigation</span>
                <span class="method-start">109986 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testRolesNavigation</span>
                <span class="method-start">117215 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testTerminalCreation</span>
                <span class="method-start">122530 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testClassCreation</span>
                <span class="method-start">163534 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testValidateToken</span>
                <span class="method-start">172708 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testRedeemToken</span>
                <span class="method-start">178596 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-class after">
                <span class="method-name">tearDown</span>
                <span class="method-start">210104 ms</span>
              </div> <!-- configuration-class after -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
      </div> <!-- main-panel-root -->
    </div> <!-- wrapper -->
  </body>
<script type="text/javascript" src="testng-reports2.js"></script>
</html>
