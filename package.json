{"name": "tvp-operator-ui-tests", "version": "1.0.0", "description": "Cypress tests for TVP Operator UI", "scripts": {"test": "cypress run", "test:chrome": "cypress run --browser chrome", "test:firefox": "cypress run --browser firefox", "test:headed": "cypress run --headed", "open": "cypress open", "clean:reports": "if exist cypress\\reports rmdir /s /q cypress\\reports && mkdir cypress\\reports", "pretest": "npm run clean:reports"}, "devDependencies": {"cypress": "^14.3.3", "cypress-xpath": "^2.0.1", "cypress-mochawesome-reporter": "^3.8.0"}}